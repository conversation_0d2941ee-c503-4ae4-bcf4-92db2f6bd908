# WebRTC TURN Server Configuration
# Copy this file to .env and update with your actual values

# Custom TURN Server (optional)
# If not set, will use Metered TURN service as fallback
# VITE_TURN_SERVER=your-turn-server-ip-or-domain
# VITE_TURN_USERNAME=webrtc
# VITE_TURN_PASSWORD=webrtc123

# Example for deployed coturn server:
# VITE_TURN_SERVER=************
# VITE_TURN_USERNAME=webrtc
# VITE_TURN_PASSWORD=webrtc123

# Server Configuration
PORT=3001

# Google Cloud Project (for deployment)
GOOGLE_CLOUD_PROJECT=your-project-id
REGION=us-central1

# TURN Server Configuration (auto-generated)
VITE_TURN_SERVER=************
VITE_TURN_USERNAME=webrtc
VITE_TURN_PASSWORD=webrtc123
