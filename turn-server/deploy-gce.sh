#!/bin/bash

# Deploy coturn TURN server to Google Compute Engine
# This script builds the Docker image locally, pushes it to GCR, and deploys to GCE

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"switcher-studio-233517"}
REGION=${REGION:-"us-central1"}
ZONE=${ZONE:-"us-central1-a"}
INSTANCE_NAME="coturn-turn-server"
MACHINE_TYPE="e2-small"
IMAGE_FAMILY="cos-stable"
IMAGE_PROJECT="cos-cloud"
NETWORK_TAG="coturn-server"
REPOSITORY_NAME="coturn-repo"
DOCKER_IMAGE="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}/coturn-turn-server"

echo "🚀 Deploying coturn TURN server to Google Compute Engine..."
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Zone: ${ZONE}"
echo "Instance: ${INSTANCE_NAME}"

# Create Artifact Registry repository if it doesn't exist
echo "📦 Setting up Artifact Registry repository..."
if ! gcloud artifacts repositories describe ${REPOSITORY_NAME} --location=${REGION} --project=${PROJECT_ID} &>/dev/null; then
    echo "Creating Artifact Registry repository: ${REPOSITORY_NAME}"
    gcloud artifacts repositories create ${REPOSITORY_NAME} \
        --repository-format=docker \
        --location=${REGION} \
        --description="Docker repository for coturn TURN server" \
        --project=${PROJECT_ID}
else
    echo "Artifact Registry repository ${REPOSITORY_NAME} already exists"
fi

# Configure Docker authentication for Artifact Registry
echo "🔐 Configuring Docker authentication..."
gcloud auth configure-docker ${REGION}-docker.pkg.dev --quiet

# Build and push Docker image
echo "📦 Building Docker image..."
docker build -t ${DOCKER_IMAGE} .

echo "📤 Pushing image to Artifact Registry..."
docker push ${DOCKER_IMAGE}

# Check if instance already exists
if gcloud compute instances describe ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} &>/dev/null; then
    echo "⚠️  Instance ${INSTANCE_NAME} already exists. Stopping and deleting..."
    gcloud compute instances delete ${INSTANCE_NAME} \
        --zone=${ZONE} \
        --project=${PROJECT_ID} \
        --quiet
    echo "✅ Existing instance deleted"
fi

# Create firewall rules for TURN server
echo "🔥 Creating firewall rules..."
gcloud compute firewall-rules delete ${NETWORK_TAG}-allow-turn --project=${PROJECT_ID} --quiet 2>/dev/null || true
gcloud compute firewall-rules create ${NETWORK_TAG}-allow-turn \
    --allow udp:3478,tcp:3478,udp:5349,tcp:5349,udp:49152-49252 \
    --source-ranges 0.0.0.0/0 \
    --target-tags ${NETWORK_TAG} \
    --description "Allow TURN server traffic" \
    --project=${PROJECT_ID}

# Create startup script for the instance
cat > startup-script.sh << EOF
#!/bin/bash

# Configure Docker authentication for Artifact Registry
gcloud auth configure-docker ${REGION}-docker.pkg.dev --quiet

# Pull the coturn image
docker pull ${DOCKER_IMAGE}

# Stop any existing coturn container
docker stop coturn-server 2>/dev/null || true
docker rm coturn-server 2>/dev/null || true

# Run coturn container with host networking for UDP support
docker run -d \\
    --name coturn-server \\
    --restart unless-stopped \\
    --network host \\
    -e TURN_USERNAME=webrtc \\
    -e TURN_PASSWORD=webrtc123 \\
    ${DOCKER_IMAGE}

echo "✅ Coturn TURN server started successfully"

# Set up log rotation
cat > /etc/logrotate.d/coturn << 'LOGROTATE_EOF'
/var/lib/docker/containers/*/*-json.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
    postrotate
        docker kill --signal="USR1" coturn-server 2>/dev/null || true
    endscript
}
LOGROTATE_EOF

echo "✅ Log rotation configured"
EOF

# Create the GCE instance
echo "🖥️  Creating GCE instance..."
gcloud compute instances create ${INSTANCE_NAME} \
    --zone=${ZONE} \
    --machine-type=${MACHINE_TYPE} \
    --network-interface=network-tier=PREMIUM,subnet=default \
    --maintenance-policy=MIGRATE \
    --provisioning-model=STANDARD \
    --service-account=$(gcloud iam service-accounts list --filter="displayName:Compute Engine default service account" --format="value(email)" --project=${PROJECT_ID}) \
    --scopes=https://www.googleapis.com/auth/devstorage.read_only,https://www.googleapis.com/auth/logging.write,https://www.googleapis.com/auth/monitoring.write,https://www.googleapis.com/auth/servicecontrol,https://www.googleapis.com/auth/service.management.readonly,https://www.googleapis.com/auth/trace.append \
    --tags=${NETWORK_TAG} \
    --image-family=${IMAGE_FAMILY} \
    --image-project=${IMAGE_PROJECT} \
    --boot-disk-size=10GB \
    --boot-disk-type=pd-balanced \
    --boot-disk-device-name=${INSTANCE_NAME} \
    --metadata-from-file startup-script=startup-script.sh \
    --project=${PROJECT_ID}

# Wait for instance to be ready
echo "⏳ Waiting for instance to start..."
sleep 45

# Get the external IP
EXTERNAL_IP=$(gcloud compute instances describe ${INSTANCE_NAME} \
    --zone=${ZONE} \
    --format='get(networkInterfaces[0].accessConfigs[0].natIP)' \
    --project=${PROJECT_ID})

# Clean up startup script
rm -f startup-script.sh

echo "✅ Deployment complete!"
echo "🔗 Instance: ${INSTANCE_NAME}"
echo "🌐 External IP: ${EXTERNAL_IP}"
echo "🎯 TURN Server: ${EXTERNAL_IP}:3478"
echo ""
echo "📝 Update your WebRTC configuration to use:"
echo "   TURN URL: turn:${EXTERNAL_IP}:3478"
echo "   Username: webrtc"
echo "   Password: webrtc123"
echo ""
echo "🔧 Management commands:"
echo "   SSH to instance: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo "   View logs: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} --command='docker logs coturn-server'"
echo "   Restart coturn: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} --command='docker restart coturn-server'"
echo "   Stop instance: gcloud compute instances stop ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo "   Delete instance: gcloud compute instances delete ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo ""
echo "🔥 Firewall rules created for ports:"
echo "   - UDP/TCP 3478 (TURN/STUN)"
echo "   - UDP/TCP 5349 (TURN/STUN TLS)"
echo "   - UDP 49152-49252 (Media relay)"
echo ""
echo "⚠️  Note: It may take a few minutes for the TURN server to be fully ready."
echo "   You can check the status with: docker logs coturn-server"
