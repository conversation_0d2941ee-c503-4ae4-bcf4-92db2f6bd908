# coturn TURN server configuration for WebRTC

# Listening port for TURN/STUN
listening-port=3478

# TLS listening port (optional, for secure connections)
tls-listening-port=5349

# Relay ports range (for media)
min-port=49152
max-port=49252

# Enable verbose logging
verbose

# Log file (will go to stdout in Docker)
log-file=stdout

# Realm for authentication
realm=webrtc.local

# Server name
server-name=turn-server

# Authentication
# Use long-term credentials
lt-cred-mech

# Static user credentials (for development)
# In production, use a database or dynamic credentials
user=webrtc:webrtc123
user=test:test123

# Allow loopback peers (for local testing)
allow-loopback-peers

# For development: Allow private IP ranges (comment out for production)
# denied-peer-ip=10.0.0.0-**************
# denied-peer-ip=***********-***************
# denied-peer-ip=**********-**************
# denied-peer-ip=*********-***************
# denied-peer-ip=***********-***************

# Allow access to all IP ranges for development
# allowed-peer-ip=0.0.0.0-***************

# Mobility support
mobility

# No multicast
no-multicast-peers

# Allow both UDP and TCP relay for maximum compatibility
# no-tcp-relay

# Enable TCP relay explicitly
tcp-relay

# Fingerprint
fingerprint

# Process limits
max-bps=1000000

# Certificate files (for TLS - optional)
# cert=/etc/coturn/cert.pem
# pkey=/etc/coturn/key.pem

# Database for user management (optional)
# userdb=/var/lib/coturn/turndb

# CLI access
cli-port=5766
cli-password=coturn123

# Disable RFC5780 support (can cause issues)
no-rfc5780

# Enable STUN
stun-only=false

# Disable software attributes
no-software-attribute
