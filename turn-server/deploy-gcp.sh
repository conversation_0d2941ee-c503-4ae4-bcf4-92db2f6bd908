#!/bin/bash

# Deploy coturn TURN server to Google Compute Engine
# This script creates a GCE instance with <PERSON><PERSON> and deploys the coturn container

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"switcher-studio-233517"}
REGION=${REGION:-"us-central1"}
ZONE=${ZONE:-"us-central1-a"}
INSTANCE_NAME="coturn-turn-server"
MACHINE_TYPE="e2-small"
IMAGE_FAMILY="cos-stable"
IMAGE_PROJECT="cos-cloud"
NETWORK_TAG="coturn-server"
REPOSITORY_NAME="coturn-repo"
DOCKER_IMAGE="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}/coturn-turn-server"

echo "🚀 Deploying coturn TURN server to Google Compute Engine..."
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Zone: ${ZONE}"
echo "Instance: ${INSTANCE_NAME}"

# Check if instance already exists
if gcloud compute instances describe ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} &>/dev/null; then
    echo "⚠️  Instance ${INSTANCE_NAME} already exists. Stopping and deleting..."
    gcloud compute instances delete ${INSTANCE_NAME} \
        --zone=${ZONE} \
        --project=${PROJECT_ID} \
        --quiet
    echo "✅ Existing instance deleted"
fi

# Create firewall rules for TURN server
echo "🔥 Creating firewall rules..."
gcloud compute firewall-rules delete ${NETWORK_TAG}-allow-turn --project=${PROJECT_ID} --quiet 2>/dev/null || true
gcloud compute firewall-rules create ${NETWORK_TAG}-allow-turn \
    --allow udp:3478,tcp:3478,udp:5349,tcp:5349,udp:49152-49252 \
    --source-ranges 0.0.0.0/0 \
    --target-tags ${NETWORK_TAG} \
    --description "Allow TURN server traffic" \
    --project=${PROJECT_ID}

# Create startup script for the instance
cat > startup-script.sh << 'EOF'
#!/bin/bash

# Update system
apt-get update

# Install Docker if not present (Container-Optimized OS should have it)
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    systemctl start docker
    systemctl enable docker
fi

# Stop any existing coturn container
docker stop coturn-server 2>/dev/null || true
docker rm coturn-server 2>/dev/null || true

# Create coturn configuration
mkdir -p /etc/coturn
cat > /etc/coturn/turnserver.conf << 'COTURN_EOF'
# coturn TURN server configuration for WebRTC

# Listening port for TURN/STUN
listening-port=3478

# TLS listening port (optional, for secure connections)
tls-listening-port=5349

# Relay ports range (for media)
min-port=49152
max-port=49252

# Enable verbose logging
verbose

# Log file (will go to stdout in Docker)
log-file=stdout

# Realm for authentication
realm=webrtc.local

# Server name
server-name=turn-server

# Authentication
# Use long-term credentials
lt-cred-mech

# Static user credentials (for development)
# In production, use a database or dynamic credentials
user=webrtc:webrtc123
user=test:test123

# Allow loopback peers (for local testing)
allow-loopback-peers

# Deny access to private IP ranges (security)
denied-peer-ip=10.0.0.0-**************
denied-peer-ip=***********-***************
denied-peer-ip=**********-**************
denied-peer-ip=*********-***************
denied-peer-ip=***********-***************

# Allow access to public IP ranges
allowed-peer-ip=0.0.0.0-***************

# Mobility support
mobility

# No multicast
no-multicast-peers

# No TCP relay (use UDP for better performance)
no-tcp-relay

# Fingerprint
fingerprint

# Process limits
max-bps=1000000

# CLI access
cli-port=5766
cli-password=coturn123

# Disable RFC5780 support (can cause issues)
no-rfc5780

# Enable STUN
stun-only=false

# Disable software attributes
no-software-attribute
COTURN_EOF

# Run coturn container
docker run -d \
    --name coturn-server \
    --restart unless-stopped \
    --network host \
    -v /etc/coturn/turnserver.conf:/etc/coturn/turnserver.conf:ro \
    -e TURN_USERNAME=webrtc \
    -e TURN_PASSWORD=webrtc123 \
    coturn/coturn:latest \
    turnserver -c /etc/coturn/turnserver.conf -v

echo "✅ Coturn TURN server started successfully"
EOF

# Create the GCE instance
echo "🖥️  Creating GCE instance..."
gcloud compute instances create ${INSTANCE_NAME} \
    --zone=${ZONE} \
    --machine-type=${MACHINE_TYPE} \
    --network-interface=network-tier=PREMIUM,subnet=default \
    --maintenance-policy=MIGRATE \
    --provisioning-model=STANDARD \
    --service-account=$(gcloud iam service-accounts list --filter="displayName:Compute Engine default service account" --format="value(email)" --project=${PROJECT_ID}) \
    --scopes=https://www.googleapis.com/auth/devstorage.read_only,https://www.googleapis.com/auth/logging.write,https://www.googleapis.com/auth/monitoring.write,https://www.googleapis.com/auth/servicecontrol,https://www.googleapis.com/auth/service.management.readonly,https://www.googleapis.com/auth/trace.append \
    --tags=${NETWORK_TAG} \
    --image-family=${IMAGE_FAMILY} \
    --image-project=${IMAGE_PROJECT} \
    --boot-disk-size=10GB \
    --boot-disk-type=pd-balanced \
    --boot-disk-device-name=${INSTANCE_NAME} \
    --metadata-from-file startup-script=startup-script.sh \
    --project=${PROJECT_ID}

# Wait for instance to be ready
echo "⏳ Waiting for instance to start..."
sleep 30

# Get the external IP
EXTERNAL_IP=$(gcloud compute instances describe ${INSTANCE_NAME} \
    --zone=${ZONE} \
    --format='get(networkInterfaces[0].accessConfigs[0].natIP)' \
    --project=${PROJECT_ID})

# Clean up startup script
rm -f startup-script.sh

echo "✅ Deployment complete!"
echo "🔗 Instance: ${INSTANCE_NAME}"
echo "🌐 External IP: ${EXTERNAL_IP}"
echo "🎯 TURN Server: ${EXTERNAL_IP}:3478"
echo ""
echo "📝 Update your WebRTC configuration to use:"
echo "   TURN URL: turn:${EXTERNAL_IP}:3478"
echo "   Username: webrtc"
echo "   Password: webrtc123"
echo ""
echo "🔧 Management commands:"
echo "   SSH to instance: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo "   View logs: gcloud compute ssh ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} --command='docker logs coturn-server'"
echo "   Stop instance: gcloud compute instances stop ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo "   Delete instance: gcloud compute instances delete ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID}"
echo ""
echo "🔥 Firewall rules created for ports:"
echo "   - UDP/TCP 3478 (TURN/STUN)"
echo "   - UDP/TCP 5349 (TURN/STUN TLS)"
echo "   - UDP 49152-49252 (Media relay)"
