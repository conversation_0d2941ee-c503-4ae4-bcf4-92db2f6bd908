version: '3.8'

services:
  coturn:
    build: .
    container_name: coturn-server
    restart: unless-stopped
    ports:
      # TURN/STUN ports
      - "3478:3478/udp"
      - "3478:3478/tcp"
      - "5349:5349/udp"
      - "5349:5349/tcp"
      # Media relay ports (reduced range for Docker)
      - "49152-49252:49152-49252/udp"
    environment:
      - TURN_USERNAME=webrtc
      - TURN_PASSWORD=webrtc123
    volumes:
      - coturn_data:/var/lib/coturn
    networks:
      - webrtc-network
    healthcheck:
      test: ["CMD", "turnutils_uclient", "-t", "-T", "-u", "webrtc", "-w", "webrtc123", "127.0.0.1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

volumes:
  coturn_data:

networks:
  webrtc-network:
    driver: bridge
