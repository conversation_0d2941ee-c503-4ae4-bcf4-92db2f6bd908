# Dockerfile for coturn TURN server
FROM ubuntu:22.04

# Install coturn and dependencies
RUN apt-get update && apt-get install -y \
    coturn \
    && rm -rf /var/lib/apt/lists/*

# Create coturn user and directories
RUN useradd -r -s /bin/false coturn && \
    mkdir -p /var/lib/coturn && \
    chown coturn:coturn /var/lib/coturn

# Copy configuration
COPY turnserver.conf /etc/coturn/turnserver.conf

# Expose TURN server ports
# 3478: TURN/STUN UDP/TCP
# 5349: TURN/STUN TLS
# 49152-65535: Media relay ports (we'll use a smaller range)
EXPOSE 3478/udp 3478/tcp 5349/udp 5349/tcp
EXPOSE 49152-49252/udp

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD turnutils_uclient -t -T -u testuser -w testpass 127.0.0.1 || exit 1

# Run coturn
USER coturn
CMD ["turnserver", "-c", "/etc/coturn/turnserver.conf", "-v"]
