# Coturn TURN Server for WebRTC

This directory contains the configuration and deployment scripts for a coturn TURN server that provides NAT traversal for WebRTC connections.

## Overview

TURN (Traversal Using Relays around NAT) servers are essential for WebRTC applications to work reliably across different network configurations. This implementation uses coturn, a popular open-source TURN server.

## Files

- `Dockerfile` - Docker container configuration for coturn
- `turnserver.conf` - Coturn server configuration
- `docker-compose.yml` - Local development setup
- `deploy-gce.sh` - Google Compute Engine deployment script
- `deploy-gcp.sh` - Legacy Cloud Run deployment (deprecated - use GCE instead)
- `configure-client.sh` - <PERSON>ript to configure WebRTC client with deployed server
- `README.md` - This documentation

## Quick Start

### 1. Deploy TURN Server to Google Cloud

```bash
cd turn-server
./deploy-gce.sh
```

This will:
- Create an Artifact Registry repository (if needed)
- Build and push the Docker image to Artifact Registry
- Create a Google Compute Engine instance
- Configure firewall rules for TURN traffic
- Deploy and start the coturn server

### 2. Configure WebRTC Client

```bash
./configure-client.sh
```

This will:
- Get the external IP of the deployed TURN server
- Update the `.env` file with TURN server configuration
- Configure the WebRTC client to use your TURN server

### 3. Test the Setup

1. Restart your development server: `npm run dev`
2. Create or join a room
3. Check browser console for TURN server connection logs

## Local Development

For local testing, use Docker Compose:

```bash
docker-compose up -d
```

This starts coturn on:
- UDP/TCP 3478 (TURN/STUN)
- UDP/TCP 5349 (TURN/STUN TLS)
- UDP 49152-49252 (Media relay)

## Configuration

### Environment Variables

The WebRTC client supports these environment variables:

- `VITE_TURN_SERVER` - TURN server IP or domain
- `VITE_TURN_USERNAME` - TURN server username (default: webrtc)
- `VITE_TURN_PASSWORD` - TURN server password (default: webrtc123)

### Security Notes

**⚠️ Important for Production:**

1. **Change default credentials** - The default username/password are for development only
2. **Use TLS certificates** - Configure TURNS (TLS) for secure connections
3. **Implement dynamic credentials** - Use time-limited credentials instead of static ones
4. **Monitor usage** - Set up logging and monitoring for the TURN server

## Deployment Options

### Google Compute Engine (Recommended)

Uses `deploy-gce.sh` to create a VM instance with Docker. This is the recommended approach because:
- Supports UDP ports (required for TURN)
- Better performance for media relay
- More control over networking

### Google Cloud Run (Not Supported)

Cloud Run doesn't support UDP ports, which are essential for TURN servers. The `deploy-gcp.sh` script is kept for reference but won't work properly.

## Troubleshooting

### Check TURN Server Status

```bash
# SSH to the instance
gcloud compute ssh coturn-turn-server --zone=us-central1-a

# Check container logs
docker logs coturn-server

# Check if ports are listening
netstat -ulnp | grep 3478
```

### Test TURN Server Connectivity

You can test the TURN server using online tools or the browser console. Look for ICE candidate gathering logs that show successful TURN server connections.

### Common Issues

1. **Firewall blocking UDP ports** - Ensure ports 3478 and 49152-49252 are open
2. **Instance not running** - Check GCE console for instance status
3. **Docker container crashed** - Check logs with `docker logs coturn-server`

## Management Commands

```bash
# View instance status
gcloud compute instances describe coturn-turn-server --zone=us-central1-a

# SSH to instance
gcloud compute ssh coturn-turn-server --zone=us-central1-a

# Restart TURN server
gcloud compute ssh coturn-turn-server --zone=us-central1-a --command='docker restart coturn-server'

# Stop instance (to save costs)
gcloud compute instances stop coturn-turn-server --zone=us-central1-a

# Delete instance
gcloud compute instances delete coturn-turn-server --zone=us-central1-a
```

## Cost Optimization

- Use `e2-small` instances for light usage
- Stop instances when not needed
- Consider preemptible instances for development
- Monitor usage with Google Cloud billing alerts

## Next Steps

1. **Implement TLS** - Add SSL certificates for TURNS support
2. **Dynamic credentials** - Implement time-limited credential generation
3. **Monitoring** - Set up logging and alerting
4. **Load balancing** - Deploy multiple instances for high availability
