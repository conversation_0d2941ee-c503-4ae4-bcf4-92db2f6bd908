<!DOCTYPE html>
<html>
<head>
    <title>TURN Server Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>TURN Server Connectivity Test</h1>
    
    <div>
        <label>TURN Server URL: <input type="text" id="turnUrl" value="turn:YOUR_GCE_EXTERNAL_IP:3478" style="width: 300px;" placeholder="turn:************:3478"></label><br><br>
        <label>Username: <input type="text" id="username" value="webrtc"></label><br><br>
        <label>Password: <input type="text" id="password" value="webrtc123"></label><br><br>
        <button onclick="testTurnServer()">Test TURN Server</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function testTurnServer() {
            const turnUrl = document.getElementById('turnUrl').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            log('Starting TURN server test...', 'info');
            
            const config = {
                iceServers: [
                    {
                        urls: turnUrl,
                        username: username,
                        credential: password
                    }
                ],
                iceTransportPolicy: 'relay' // Force TURN only
            };

            try {
                const pc = new RTCPeerConnection(config);
                
                let candidateCount = 0;
                let relayCount = 0;
                
                pc.onicecandidate = (event) => {
                    if (event.candidate) {
                        candidateCount++;
                        const candidate = event.candidate.candidate;
                        
                        if (candidate.includes('typ relay')) {
                            relayCount++;
                            log(`✅ TURN relay candidate: ${candidate}`, 'success');
                        } else {
                            log(`⚠️ Non-relay candidate: ${candidate}`, 'warning');
                        }
                    } else {
                        log(`ICE gathering complete. Total: ${candidateCount}, Relay: ${relayCount}`, 
                            relayCount > 0 ? 'success' : 'error');
                        
                        if (relayCount === 0) {
                            log('❌ No TURN relay candidates found! TURN server may not be working.', 'error');
                        } else {
                            log(`✅ TURN server is working! Found ${relayCount} relay candidates.`, 'success');
                        }
                    }
                };

                pc.onicegatheringstatechange = () => {
                    log(`ICE gathering state: ${pc.iceGatheringState}`, 'info');
                };

                pc.oniceconnectionstatechange = () => {
                    log(`ICE connection state: ${pc.iceConnectionState}`, 'info');
                };

                // Create a data channel to trigger ICE gathering
                const dataChannel = pc.createDataChannel('test');
                
                // Create offer to start ICE gathering
                const offer = await pc.createOffer();
                await pc.setLocalDescription(offer);
                
                log('Created offer and set local description. ICE gathering should start...', 'info');
                
                // Timeout after 10 seconds
                setTimeout(() => {
                    if (pc.iceGatheringState !== 'complete') {
                        log('⚠️ ICE gathering timeout after 10 seconds', 'warning');
                    }
                    pc.close();
                }, 10000);
                
            } catch (error) {
                log(`❌ Error testing TURN server: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
