import React, { useState, useEffect, useRef } from 'react';
import { Terminal, X, Minimize2, Maximize2, Trash2, Co<PERSON>, Check, Filter, Eye, EyeOff } from 'lucide-react';

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug' | 'success';
  category: string;
  message: string;
  data?: any;
}

interface LogConsoleProps {
  logs: LogEntry[];
  filteredLogs: LogEntry[];
  availableCategories: string[];
  enabledCategories: Set<string>;
  onClear: () => void;
  onToggleCategory: (category: string) => void;
  onEnableAllCategories: () => void;
  onDisableAllCategories: () => void;
}

export const LogConsole: React.FC<LogConsoleProps> = ({
  logs,
  filteredLogs,
  availableCategories,
  enabledCategories,
  onClear,
  onToggleCategory,
  onEnableAllCategories,
  onDisableAllCategories
}) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const logContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [filteredLogs]);

  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return 'text-red-400';
      case 'warn': return 'text-yellow-400';
      case 'success': return 'text-green-400';
      case 'debug': return 'text-blue-400';
      case 'info':
      default: return 'text-gray-300';
    }
  };

  const getLevelBg = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return 'bg-red-500/10 border-red-500/20';
      case 'warn': return 'bg-yellow-500/10 border-yellow-500/20';
      case 'success': return 'bg-green-500/10 border-green-500/20';
      case 'debug': return 'bg-blue-500/10 border-blue-500/20';
      case 'info':
      default: return 'bg-gray-500/10 border-gray-500/20';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  const copyLogsToClipboard = async () => {
    try {
      // Format filtered logs as plain text
      const logText = filteredLogs.map(log => {
        const timestamp = formatTime(log.timestamp);
        const level = log.level.toUpperCase();
        const category = log.category;
        const message = log.message;
        const data = log.data ? `\nData: ${JSON.stringify(log.data, null, 2)}` : '';

        return `[${timestamp}] [${level}] ${category}: ${message}${data}`;
      }).join('\n\n');

      await navigator.clipboard.writeText(logText);
      setIsCopied(true);

      // Reset the copied state after 2 seconds
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy logs to clipboard:', error);
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = filteredLogs.map(log => {
        const timestamp = formatTime(log.timestamp);
        const level = log.level.toUpperCase();
        const category = log.category;
        const message = log.message;
        const data = log.data ? `\nData: ${JSON.stringify(log.data, null, 2)}` : '';

        return `[${timestamp}] [${level}] ${category}: ${message}${data}`;
      }).join('\n\n');

      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    }
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsMinimized(false)}
          className="bg-gray-800/90 backdrop-blur-lg border border-gray-700 rounded-lg p-3 text-white hover:bg-gray-700/90 transition-colors flex items-center space-x-2"
        >
          <Terminal className="w-5 h-5" />
          <span className="text-sm">Console ({filteredLogs.length}/{logs.length})</span>
        </button>
      </div>
    );
  }

  const containerClasses = isExpanded 
    ? "fixed inset-4 z-50" 
    : "fixed bottom-4 right-4 w-96 h-80 z-50";

  return (
    <div className={containerClasses}>
      <div className="bg-gray-900/95 backdrop-blur-lg border border-gray-700 rounded-lg h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-700">
          <div className="flex items-center space-x-2">
            <Terminal className="w-5 h-5 text-green-400" />
            <span className="text-white font-medium">Debug Console</span>
            <span className="text-xs text-gray-400">({filteredLogs.length}/{logs.length} entries)</span>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`p-1 transition-colors ${
                showFilters
                  ? 'text-blue-400'
                  : 'text-gray-400 hover:text-white'
              }`}
              title="Toggle category filters"
            >
              <Filter className="w-4 h-4" />
            </button>
            <button
              onClick={copyLogsToClipboard}
              className={`p-1 transition-colors ${
                isCopied
                  ? 'text-green-400'
                  : 'text-gray-400 hover:text-white'
              }`}
              title={isCopied ? "Copied!" : "Copy logs to clipboard"}
            >
              {isCopied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
            </button>
            <button
              onClick={onClear}
              className="p-1 text-gray-400 hover:text-white transition-colors"
              title="Clear logs"
            >
              <Trash2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 text-gray-400 hover:text-white transition-colors"
              title={isExpanded ? "Restore" : "Expand"}
            >
              {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
            <button
              onClick={() => setIsMinimized(true)}
              className="p-1 text-gray-400 hover:text-white transition-colors"
              title="Minimize"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Category Filters */}
        {showFilters && (
          <div className="border-b border-gray-700 p-3 bg-gray-800/50">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-300 font-medium">Category Filters</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={onEnableAllCategories}
                  className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                >
                  All
                </button>
                <span className="text-gray-500">|</span>
                <button
                  onClick={onDisableAllCategories}
                  className="text-xs text-red-400 hover:text-red-300 transition-colors"
                >
                  None
                </button>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {availableCategories.map(category => {
                const isEnabled = enabledCategories.has(category);
                const categoryLogs = logs.filter(log => log.category === category);
                return (
                  <button
                    key={category}
                    onClick={() => onToggleCategory(category)}
                    className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
                      isEnabled
                        ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                        : 'bg-gray-700/50 text-gray-400 border border-gray-600/30 hover:bg-gray-600/50'
                    }`}
                  >
                    {isEnabled ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
                    <span>{category}</span>
                    <span className="text-xs opacity-70">({categoryLogs.length})</span>
                  </button>
                );
              })}
            </div>
            {availableCategories.length === 0 && (
              <div className="text-xs text-gray-500 text-center py-2">
                No categories available yet
              </div>
            )}
          </div>
        )}

        {/* Log Content */}
        <div 
          ref={logContainerRef}
          className="flex-1 overflow-y-auto p-2 space-y-1 font-mono text-xs"
        >
          {filteredLogs.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              {logs.length === 0
                ? "No logs yet. Waiting for activity..."
                : "No logs match the current filters. Try enabling more categories."
              }
            </div>
          ) : (
            filteredLogs.map((log) => (
              <div
                key={log.id}
                className={`p-2 rounded border ${getLevelBg(log.level)}`}
              >
                <div className="flex items-start space-x-2">
                  <span className="text-gray-500 text-xs shrink-0">
                    {formatTime(log.timestamp)}
                  </span>
                  <span className={`text-xs font-bold shrink-0 ${getLevelColor(log.level)}`}>
                    [{log.level.toUpperCase()}]
                  </span>
                  <span className="text-purple-400 text-xs shrink-0">
                    {log.category}:
                  </span>
                </div>
                <div className={`mt-1 ${getLevelColor(log.level)}`}>
                  {log.message}
                </div>
                {log.data && (
                  <details className="mt-1">
                    <summary className="text-gray-400 cursor-pointer text-xs">
                      Show data
                    </summary>
                    <pre className="text-gray-300 text-xs mt-1 overflow-x-auto">
                      {JSON.stringify(log.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
