import React, { createContext, useContext, useState, useCallback } from 'react';
import { LogEntry } from '../components/LogConsole';

interface LogContextType {
  logs: LogEntry[];
  addLog: (level: LogEntry['level'], category: string, message: string, data?: any) => void;
  clearLogs: () => void;
  filteredLogs: LogEntry[];
  availableCategories: string[];
  enabledCategories: Set<string>;
  toggleCategory: (category: string) => void;
  enableAllCategories: () => void;
  disableAllCategories: () => void;
}

const LogContext = createContext<LogContextType | null>(null);

export const useLog = () => {
  const context = useContext(LogContext);
  if (!context) {
    throw new Error('useLog must be used within a LogProvider');
  }
  return context;
};

export const LogProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [enabledCategories, setEnabledCategories] = useState<Set<string>>(new Set());

  const addLog = useCallback((level: LogEntry['level'], category: string, message: string, data?: any) => {
    const newLog: LogEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      timestamp: new Date(),
      level,
      category,
      message,
      data
    };

    setLogs(prev => {
      // Keep only the last 500 logs to prevent memory issues
      const newLogs = [...prev, newLog];
      return newLogs.length > 500 ? newLogs.slice(-500) : newLogs;
    });

    // Auto-enable new categories
    setEnabledCategories(prev => {
      if (!prev.has(category)) {
        return new Set([...prev, category]);
      }
      return prev;
    });

    // Also log to browser console for debugging
    const consoleMessage = `[${category}] ${message}`;
    switch (level) {
      case 'error':
        console.error(consoleMessage, data);
        break;
      case 'warn':
        console.warn(consoleMessage, data);
        break;
      case 'debug':
        console.debug(consoleMessage, data);
        break;
      case 'success':
      case 'info':
      default:
        console.log(consoleMessage, data);
        break;
    }
  }, []);

  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  // Get all unique categories from logs
  const availableCategories = React.useMemo(() => {
    const categories = new Set(logs.map(log => log.category));
    return Array.from(categories).sort();
  }, [logs]);

  // Filter logs based on enabled categories
  const filteredLogs = React.useMemo(() => {
    return logs.filter(log => enabledCategories.has(log.category));
  }, [logs, enabledCategories]);

  const toggleCategory = useCallback((category: string) => {
    setEnabledCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(category)) {
        newSet.delete(category);
      } else {
        newSet.add(category);
      }
      return newSet;
    });
  }, []);

  const enableAllCategories = useCallback(() => {
    setEnabledCategories(new Set(availableCategories));
  }, [availableCategories]);

  const disableAllCategories = useCallback(() => {
    setEnabledCategories(new Set());
  }, []);

  return (
    <LogContext.Provider value={{
      logs,
      addLog,
      clearLogs,
      filteredLogs,
      availableCategories,
      enabledCategories,
      toggleCategory,
      enableAllCategories,
      disableAllCategories
    }}>
      {children}
    </LogContext.Provider>
  );
};
