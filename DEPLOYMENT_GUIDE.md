# Deployment Guide - WebRTC with TURN Server

This guide walks you through deploying both the WebRTC application and the coturn TURN server to Google Cloud Platform.

## Prerequisites

- Google Cloud SDK installed and configured
- Docker installed
- Node.js 18+ installed
- Google Cloud project with billing enabled

## Step 1: Deploy TURN Server

The TURN server is essential for WebRTC connections to work reliably across different networks.

```bash
# Navigate to TURN server directory
cd turn-server

# Deploy to Google Compute Engine
./deploy-gce.sh
```

This will:
- Create an Artifact Registry repository (if needed)
- Build and push the coturn Docker image to Artifact Registry
- Create a GCE instance with the TURN server
- Configure firewall rules for TURN traffic
- Output the server IP and connection details

## Step 2: Configure WebRTC Client

After the TURN server is deployed, configure the WebRTC client to use it:

```bash
# Still in turn-server directory
./configure-client.sh
```

This will:
- Get the external IP of the deployed TURN server
- Update the `.env` file with TURN server configuration
- Configure the WebRTC client automatically

## Step 3: Deploy WebRTC Application

Deploy the main WebRTC streaming application:

```bash
# Navigate back to project root
cd ..

# Build and deploy to Cloud Run
npm run deploy
```

## Step 4: Test the Setup

1. Open the deployed WebRTC application URL
2. Create a room as a host
3. Join the room from another browser/device as a participant
4. Check browser console for TURN server connection logs

## Environment Variables

The application supports these environment variables:

### Client (Frontend)
- `VITE_TURN_SERVER` - TURN server IP (auto-configured by configure-client.sh)
- `VITE_TURN_USERNAME` - TURN server username (default: webrtc)
- `VITE_TURN_PASSWORD` - TURN server password (default: webrtc123)

### Server (Backend)
- `PORT` - Server port (default: 3001)
- `GOOGLE_CLOUD_PROJECT` - GCP project ID
- `REGION` - GCP region (default: us-central1)

## Cost Management

### TURN Server (GCE)
- Instance type: e2-small (~$13/month if running 24/7)
- Stop when not needed: `gcloud compute instances stop coturn-turn-server --zone=us-central1-a`
- Start when needed: `gcloud compute instances start coturn-turn-server --zone=us-central1-a`

### WebRTC App (Cloud Run)
- Pay per request (very cost-effective for development)
- Scales to zero when not in use

## Troubleshooting

### TURN Server Issues
```bash
# Check TURN server status
gcloud compute ssh coturn-turn-server --zone=us-central1-a --command='docker logs coturn-server'

# Test connectivity
gcloud compute ssh coturn-turn-server --zone=us-central1-a --command='netstat -ulnp | grep 3478'
```

### WebRTC Connection Issues
1. Check browser console for ICE candidate logs
2. Verify TURN server is running and accessible
3. Ensure firewall rules are properly configured
4. Test with different networks (mobile vs WiFi)

## Security Notes

**⚠️ For Production Use:**

1. **Change TURN credentials** - Update username/password in turnserver.conf
2. **Enable TLS** - Configure SSL certificates for TURNS
3. **Implement dynamic credentials** - Use time-limited authentication
4. **Monitor usage** - Set up logging and billing alerts
5. **Use HTTPS** - Ensure WebRTC app is served over HTTPS

## Next Steps

1. **TLS Configuration** - Set up SSL certificates for the TURN server
2. **Dynamic Credentials** - Implement REST API for credential generation
3. **Monitoring** - Add logging and alerting for both services
4. **Load Balancing** - Deploy multiple TURN servers for high availability
5. **CDN Integration** - Use Cloud CDN for global distribution

## Cleanup

To remove all deployed resources:

```bash
# Delete TURN server
gcloud compute instances delete coturn-turn-server --zone=us-central1-a

# Delete firewall rules
gcloud compute firewall-rules delete coturn-server-allow-turn

# Delete Cloud Run service
gcloud run services delete poc-webrtc --region=us-central1

# Delete container images (optional)
gcloud artifacts docker images delete us-central1-docker.pkg.dev/YOUR_PROJECT_ID/coturn-repo/coturn-turn-server
gcloud container images delete gcr.io/YOUR_PROJECT_ID/poc-webrtc
```
